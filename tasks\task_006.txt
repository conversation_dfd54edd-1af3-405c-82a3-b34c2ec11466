# Task ID: 6
# Title: Implement School Setup Wizard
# Status: pending
# Dependencies: 5
# Priority: medium
# Description: Create a school setup wizard that allows instructors to configure their branded school with custom domain, branding elements, and settings.
# Details:
Develop a school setup wizard for instructors:

1. Create a multi-step wizard for school configuration
2. Step 1: Basic information (name, description, category)
3. Step 2: Branding (logo upload, color scheme selection)
4. Step 4: Subdomain selection with availability check
5. Step 5: Settings (privacy, enrollment options)
6. Create Schools table in Supabase with required fields
7. Implement logo and image uploads to Supabase Storage
8. Add subdomain validation and reservation

Implement subdomain routing using Next.js middleware. Use a color picker component for branding customization. Create a preview component to show school appearance in real-time.

Key libraries and technologies:
- react-hook-form@7.48.2
- zod@3.22.4
- react-colorful@5.6.1 (for color picking)
- @supabase/storage-js (for image uploads)
- next-subdomain-router (for subdomain handling)

Implement as client components with server actions for form submission and subdomain validation.

# Test Strategy:
1. Unit tests for form validation in each step
2. Integration tests for wizard navigation
3. Test subdomain validation and reservation
4. Verify image upload functionality
5. Test color scheme selection and preview
6. Validate database updates after submission
7. End-to-end test of complete school setup process
