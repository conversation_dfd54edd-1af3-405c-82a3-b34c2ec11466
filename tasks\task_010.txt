# Task ID: 10
# Title: Implement Course Browsing and Search
# Status: pending
# Dependencies: 8, 9
# Priority: medium
# Description: Create a course catalog with search, filtering, and discovery features to help students find relevant courses across schools.
# Details:
Develop a comprehensive course browsing experience:

1. Course catalog with grid and list views
2. Advanced search functionality with instant results
3. Filtering by category, price, rating, and duration
4. School-specific course listings
5. Featured and trending courses sections
6. Pagination and infinite scrolling options
7. Search engine optimization for course discovery

Implement server-side search using Supabase's full-text search capabilities. Use debounced search input for performance. Implement filtering with URL query parameters for shareable search results.

Key libraries:
- @tanstack/react-query@5.8.4 (for data fetching)
- use-debounce@9.0.4 (for search input)
- @tanstack/react-table@8.10.7 (for data tables)
- next/navigation (for URL parameter handling)

Implement as server components with client islands for interactive elements.

# Test Strategy:
1. Unit tests for search and filter components
2. Integration tests for search functionality
3. Test filter combinations
4. Verify pagination and infinite scrolling
5. Test search performance with large datasets
6. Validate URL parameter handling
7. End-to-end test of course discovery workflow
