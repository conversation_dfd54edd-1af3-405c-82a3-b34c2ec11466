{"tasks": [{"id": 1, "title": "Create <PERSON>", "description": "Develop a modern, responsive landing page that clearly communicates the platform's value proposition to all user types (students, instructors, and administrators).", "details": "Implement using Next.js 14 App Router with TypeScript. Create a responsive layout using Tailwind CSS and Shadcn UI components. The landing page should include:\n\n1. Hero section with clear value proposition\n2. Feature highlights for each user type\n3. Call-to-action buttons for registration/login\n4. Responsive navigation with mobile menu\n5. Footer with essential links\n\nImplement SEO optimization with Next.js metadata API. Ensure RTL support for Arabic content using the `dir` attribute and Tailwind's RTL plugin. Use Next.js Image component for optimized image loading.\n\nKey libraries:\n- next@14.0.3\n- react@18.2.0\n- tailwindcss@3.3.5\n- @radix-ui/react-* (latest versions for Shadcn UI)\n- next-themes for dark mode support\n\nImplement as a server component where possible for improved performance.", "testStrategy": "1. Unit tests for UI components using React Testing Library\n2. Responsive testing across mobile, tablet, and desktop viewports\n3. RTL layout verification\n4. Lighthouse performance and accessibility audits\n5. Cross-browser compatibility testing (Chrome, Firefox, Safari, Edge)\n6. Manual testing of navigation and interactive elements", "priority": "high", "dependencies": [], "status": "pending", "subtasks": []}, {"id": 2, "title": "Implement User Registration System", "description": "Create a comprehensive user registration system with role selection (Student, Instructor, Admin) and email verification functionality.", "details": "Implement user registration using Supabase Auth and Database:\n\n1. Create registration form with fields: name, email, password, role selection\n2. Set up form validation using react-hook-form and zod\n3. Implement Supabase Auth signUp method with email confirmation\n4. Create users table in Supabase with required fields (id, email, name, role, status, created_at)\n5. Add role-based redirection after registration\n6. Implement error handling and user feedback\n\nCreate custom registration hooks and context providers for auth state management. Use Supabase Row Level Security (RLS) policies to secure user data.\n\nKey libraries and versions:\n- @supabase/auth-helpers-nextjs@0.8.1\n- @supabase/supabase-js@2.38.4\n- react-hook-form@7.48.2\n- zod@3.22.4\n\nImplement as client components with server actions for form submission.", "testStrategy": "1. Unit tests for form validation logic\n2. Integration tests for registration flow\n3. Test error handling for duplicate emails\n4. Verify role selection functionality\n5. Test form validation for all required fields\n6. End-to-end test of complete registration process\n7. Security testing for proper data handling", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": []}, {"id": 3, "title": "Build Login System with Role-Based Redirects", "description": "Develop a secure login system with role-based redirects, session management, and error handling.", "details": "Implement login functionality using Supabase Auth:\n\n1. Create login form with email/password fields\n2. Implement form validation with react-hook-form and zod\n3. Use Supabase signInWithPassword method for authentication\n4. Set up middleware for role-based redirects:\n   - Students → Student Dashboard\n   - Instructors → Instructor Dashboard\n   - Admins → Admin Dashboard\n5. Implement session persistence with Supabase Auth Helpers\n6. Add \"Remember me\" functionality\n7. Create password reset flow\n\nUse Next.js middleware to protect routes based on authentication status and user role. Implement CSRF protection and rate limiting for security.\n\nKey libraries:\n- @supabase/auth-helpers-nextjs@0.8.1\n- @supabase/supabase-js@2.38.4\n- react-hook-form@7.48.2\n- zod@3.22.4\n- next-auth@4.24.5 (optional for additional auth features)\n\nImplement as client components with server actions for form submission.", "testStrategy": "1. Unit tests for login form validation\n2. Integration tests for authentication flow\n3. Test role-based redirects for each user type\n4. Verify error handling for invalid credentials\n5. Test session persistence across page refreshes\n6. Security testing for brute force protection\n7. End-to-end test of complete login process", "priority": "high", "dependencies": [2], "status": "pending", "subtasks": []}, {"id": 4, "title": "Implement Email Verification Flow", "description": "Create an email verification system with secure token handling, verification status tracking, and resend functionality.", "details": "Implement email verification using Supabase Auth and a transactional email service:\n\n1. Set up Supabase Auth email templates for verification\n2. Create verification page that handles token validation\n3. Update user status in database upon successful verification\n4. Implement resend verification email functionality\n5. Add verification status checks in protected routes\n6. Create user feedback for verification states\n\nUse Supabase's built-in email verification flow with custom templates. Implement server-side verification token validation for security.\n\nFor email delivery, configure Supabase to use a reliable email service like SendGrid, Postmark, or Resend.com for high deliverability.\n\nKey libraries and services:\n- @supabase/auth-helpers-nextjs@0.8.1\n- @supabase/supabase-js@2.38.4\n- Resend.com or SendGrid for transactional emails\n\nImplement as a mix of client and server components with server actions for secure token handling.", "testStrategy": "1. Unit tests for token validation logic\n2. Integration tests for verification flow\n3. Test resend functionality\n4. Verify database updates after verification\n5. Test expiration handling for verification tokens\n6. End-to-end test of complete verification process\n7. Security testing for token tampering protection", "priority": "high", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 5, "title": "Create Instructor Application Wizard", "description": "Develop a 3-step instructor application wizard that collects personal information, teaching experience, and motivation for becoming an instructor.", "details": "Implement a multi-step application wizard for instructor verification:\n\n1. Create a wizard component with step tracking and navigation\n2. Step 1: Personal information form (bio, profile picture, credentials)\n3. Step 2: Teaching experience form (subjects, years of experience, certifications)\n4. Step 3: Motivation statement and agreement to terms\n5. Implement form validation for each step using react-hook-form and zod\n6. Create Applications table in Supabase with required fields\n7. Add file upload for supporting documents using Supabase Storage\n8. Implement application submission with status tracking\n\nUse a state management approach like React Context or Zustand to maintain form state across steps. Implement auto-save functionality to prevent data loss.\n\nKey libraries:\n- react-hook-form@7.48.2\n- zod@3.22.4\n- zustand@4.4.6 (for state management)\n- @supabase/storage-js (for file uploads)\n- react-dropzone@14.2.3 (for file upload UI)\n\nImplement as client components with server actions for form submission.", "testStrategy": "1. Unit tests for form validation in each step\n2. Integration tests for wizard navigation\n3. Test file upload functionality\n4. Verify data persistence between steps\n5. Test form submission and database updates\n6. Validate error handling and user feedback\n7. End-to-end test of complete application process", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 6, "title": "Implement School Setup Wizard", "description": "Create a school setup wizard that allows instructors to configure their branded school with custom domain, branding elements, and settings.", "details": "Develop a school setup wizard for instructors:\n\n1. <PERSON>reate a multi-step wizard for school configuration\n2. Step 1: Basic information (name, description, category)\n3. Step 2: Branding (logo upload, color scheme selection)\n4. Step 4: Subdomain selection with availability check\n5. Step 5: Settings (privacy, enrollment options)\n6. Create Schools table in Supabase with required fields\n7. Implement logo and image uploads to Supabase Storage\n8. Add subdomain validation and reservation\n\nImplement subdomain routing using Next.js middleware. Use a color picker component for branding customization. Create a preview component to show school appearance in real-time.\n\nKey libraries and technologies:\n- react-hook-form@7.48.2\n- zod@3.22.4\n- react-colorful@5.6.1 (for color picking)\n- @supabase/storage-js (for image uploads)\n- next-subdomain-router (for subdomain handling)\n\nImplement as client components with server actions for form submission and subdomain validation.", "testStrategy": "1. Unit tests for form validation in each step\n2. Integration tests for wizard navigation\n3. Test subdomain validation and reservation\n4. Verify image upload functionality\n5. Test color scheme selection and preview\n6. Validate database updates after submission\n7. End-to-end test of complete school setup process", "priority": "medium", "dependencies": [5], "status": "pending", "subtasks": []}, {"id": 7, "title": "Build Instructor Dashboard", "description": "Create a comprehensive instructor dashboard with course management, student analytics, and revenue tracking.", "details": "Implement an instructor dashboard with the following features:\n\n1. Dashboard overview with key metrics (students, courses, revenue)\n2. Course management section (list, create, edit, delete)\n3. Student management with enrollment data\n4. Analytics section with charts for engagement and revenue\n5. Payout history and upcoming payments\n6. School settings management\n7. Real-time notifications using Supabase Realtime\n\nUse a dashboard layout with responsive sidebar navigation. Implement data visualization with Recharts or Chart.js. Use Supabase real-time subscriptions for live updates.\n\nKey libraries:\n- recharts@2.9.3 (for data visualization)\n- @tanstack/react-table@8.10.7 (for data tables)\n- @supabase/realtime-js (for real-time updates)\n- date-fns@2.30.0 (for date formatting)\n\nImplement as a mix of client and server components, with server components for data fetching and client components for interactive elements.", "testStrategy": "1. Unit tests for dashboard components\n2. Integration tests for data fetching and display\n3. Test real-time update functionality\n4. Verify responsive layout across device sizes\n5. Test data visualization components\n6. Validate navigation and routing\n7. End-to-end test of instructor workflows", "priority": "medium", "dependencies": [6], "status": "pending", "subtasks": []}, {"id": 8, "title": "Develop Course Creation Tools", "description": "Build a rich course builder with support for video, text, and PDF content, including drag-and-drop lesson organization and Bunny.net integration.", "details": "Implement comprehensive course creation tools:\n\n1. Course structure builder with sections and lessons\n2. Rich text editor for lesson content using TipTap or Lexical\n3. Video upload integration with Bunny.net Stream\n4. PDF and document upload functionality\n5. Drag-and-drop lesson reordering using dnd-kit\n6. Auto-save functionality with optimistic UI updates\n7. Course settings (pricing, visibility, requirements)\n8. Course preview functionality\n\nIntegrate with Bunny.net for video processing and streaming. Use their adaptive bitrate streaming for optimal playback across devices. Implement client-side and server-side validation for course structure.\n\nKey libraries and services:\n- @tiptap/react@2.1.12 (for rich text editing)\n- @dnd-kit/core@6.0.8 and @dnd-kit/sortable@7.0.2 (for drag-and-drop)\n- bunny-stream-js@1.0.0 (for Bunny.net integration)\n- react-dropzone@14.2.3 (for file uploads)\n- @tanstack/react-query@5.8.4 (for data fetching and caching)\n\nImplement as client components with server actions for data persistence.", "testStrategy": "1. Unit tests for editor components\n2. Integration tests for content saving and retrieval\n3. Test drag-and-drop functionality\n4. Verify video upload and processing\n5. Test auto-save functionality\n6. Validate course structure validation\n7. End-to-end test of course creation workflow\n8. Performance testing for large courses", "priority": "high", "dependencies": [7], "status": "pending", "subtasks": []}, {"id": 9, "title": "Create Student Dashboard", "description": "Develop a student dashboard that displays enrolled courses, learning progress, and personalized recommendations.", "details": "Implement a student dashboard with the following features:\n\n1. Dashboard overview with learning progress and stats\n2. Enrolled courses list with progress indicators\n3. Continue learning section for quick access to current courses\n4. Course recommendations based on interests and history\n5. Learning calendar and schedule\n6. Achievement and certificate display\n7. Notification center for course updates\n\nUse server components for initial data loading and client components for interactive elements. Implement skeleton loading states for improved perceived performance.\n\nKey libraries:\n- @tanstack/react-query@5.8.4 (for data fetching)\n- date-fns@2.30.0 (for date handling)\n- recharts@2.9.3 (for progress visualization)\n- @supabase/realtime-js (for real-time notifications)\n\nImplement as a mix of server and client components for optimal performance.", "testStrategy": "1. Unit tests for dashboard components\n2. Integration tests for data fetching and display\n3. Test progress tracking functionality\n4. Verify notification system\n5. Test responsive layout across device sizes\n6. Validate navigation and routing\n7. End-to-end test of student workflows", "priority": "medium", "dependencies": [4], "status": "pending", "subtasks": []}, {"id": 10, "title": "Implement Course Browsing and Search", "description": "Create a course catalog with search, filtering, and discovery features to help students find relevant courses across schools.", "details": "Develop a comprehensive course browsing experience:\n\n1. Course catalog with grid and list views\n2. Advanced search functionality with instant results\n3. Filtering by category, price, rating, and duration\n4. School-specific course listings\n5. Featured and trending courses sections\n6. Pagination and infinite scrolling options\n7. Search engine optimization for course discovery\n\nImplement server-side search using Supabase's full-text search capabilities. Use debounced search input for performance. Implement filtering with URL query parameters for shareable search results.\n\nKey libraries:\n- @tanstack/react-query@5.8.4 (for data fetching)\n- use-debounce@9.0.4 (for search input)\n- @tanstack/react-table@8.10.7 (for data tables)\n- next/navigation (for URL parameter handling)\n\nImplement as server components with client islands for interactive elements.", "testStrategy": "1. Unit tests for search and filter components\n2. Integration tests for search functionality\n3. Test filter combinations\n4. Verify pagination and infinite scrolling\n5. Test search performance with large datasets\n6. Validate URL parameter handling\n7. End-to-end test of course discovery workflow", "priority": "medium", "dependencies": [8, 9], "status": "pending", "subtasks": []}, {"id": 11, "title": "Build Course Detail Pages", "description": "Create detailed course pages with curriculum preview, instructor information, student reviews, and enrollment functionality.", "details": "Implement comprehensive course detail pages:\n\n1. Course header with title, instructor, and key metrics\n2. Course description and learning objectives\n3. Curriculum preview with expandable sections\n4. Instructor profile and credentials\n5. Student reviews and ratings\n6. Related and recommended courses\n7. Enrollment and payment options\n8. Preview video or sample lesson\n\nUse Open Graph tags and structured data for improved social sharing and SEO. Implement video previews using Bunny.net's player. Create a responsive layout that works well on all devices.\n\nKey libraries and technologies:\n- bunny-stream-player@1.0.0 (for video preview)\n- schema-dts@1.1.2 (for structured data)\n- next/metadata (for SEO optimization)\n- @vercel/og (for dynamic Open Graph images)\n\nImplement as server components with client islands for interactive elements.", "testStrategy": "1. Unit tests for page components\n2. Integration tests for data fetching and display\n3. Test video preview functionality\n4. Verify responsive layout across device sizes\n5. Test SEO elements and structured data\n6. Validate enrollment flow\n7. End-to-end test of course detail page experience", "priority": "medium", "dependencies": [8, 10], "status": "pending", "subtasks": []}, {"id": 12, "title": "Develop Interactive Course Player", "description": "Create an interactive course player with video streaming, progress tracking, note-taking, and completion management.", "details": "Implement a feature-rich course player:\n\n1. Video player integration with Bunny.net Stream\n2. Lesson navigation with progress indicators\n3. Automatic progress tracking and resumption\n4. Note-taking functionality with timestamp linking\n5. Interactive elements (quizzes, checkpoints)\n6. Playback speed and quality controls\n7. Offline viewing capabilities (where applicable)\n8. Completion tracking and certificate generation\n\nUse Bunny.net's player SDK for video streaming with adaptive bitrate. Implement client-side progress tracking with server synchronization. Create a responsive player that works well on mobile devices.\n\nKey libraries and technologies:\n- bunny-stream-player@1.0.0 (for video playback)\n- @tiptap/react@2.1.12 (for note-taking)\n- @tanstack/react-query@5.8.4 (for data synchronization)\n- browser-db (IndexedDB wrapper for offline support)\n\nImplement as client components with server actions for progress synchronization.", "testStrategy": "1. Unit tests for player components\n2. Integration tests for progress tracking\n3. Test video playback across devices and connections\n4. Verify note-taking functionality\n5. Test offline capabilities\n6. Validate completion tracking\n7. Performance testing for video streaming\n8. End-to-end test of learning experience", "priority": "high", "dependencies": [8, 11], "status": "pending", "subtasks": []}, {"id": 13, "title": "Create Admin Dashboard", "description": "Build a comprehensive admin dashboard with platform metrics, user statistics, and management tools.", "details": "Implement an admin dashboard with the following features:\n\n1. Dashboard overview with key platform metrics\n2. User growth and engagement analytics\n3. Revenue and financial reporting\n4. System health monitoring\n5. Content moderation tools\n6. Settings and configuration management\n7. Admin activity logs and audit trails\n\nUse server components for data fetching and client components for interactive elements. Implement role-based access control to restrict dashboard access to administrators only.\n\nKey libraries:\n- recharts@2.9.3 (for data visualization)\n- @tanstack/react-table@8.10.7 (for data tables)\n- date-fns@2.30.0 (for date formatting)\n- @tremor/react@3.11.1 (for dashboard components)\n\nImplement as a mix of server and client components for optimal performance.", "testStrategy": "1. Unit tests for dashboard components\n2. Integration tests for data fetching and display\n3. Test role-based access control\n4. Verify analytics calculations\n5. Test responsive layout across device sizes\n6. Validate navigation and routing\n7. End-to-end test of admin workflows", "priority": "medium", "dependencies": [3], "status": "pending", "subtasks": []}, {"id": 14, "title": "Implement Instructor Application Review System", "description": "Create a system for administrators to review, approve, or reject instructor applications with feedback capabilities.", "details": "Develop an instructor application review system:\n\n1. Application queue with status filtering\n2. Detailed application view with all submitted information\n3. Document viewer for supporting materials\n4. Approval/rejection workflow with reason selection\n5. Feedback mechanism for rejected applications\n6. Automatic email notifications for status changes\n7. Application statistics and processing metrics\n\nImplement a workflow system for application states (Pending, Under Review, Approved, Rejected). Use Supabase real-time features for live updates to the application queue.\n\nKey libraries and technologies:\n- @tanstack/react-table@8.10.7 (for application queue)\n- react-pdf@7.5.1 (for document viewing)\n- @supabase/realtime-js (for real-time updates)\n- Resend.com or SendGrid (for email notifications)\n\nImplement as client components with server actions for application processing.", "testStrategy": "1. Unit tests for review components\n2. Integration tests for application processing\n3. Test email notification system\n4. Verify real-time updates\n5. Test document viewing functionality\n6. Validate workflow state transitions\n7. End-to-end test of application review process", "priority": "medium", "dependencies": [5, 13], "status": "pending", "subtasks": []}, {"id": 15, "title": "Build User Management System", "description": "Create a comprehensive user management system for administrators to view, edit, and manage all platform users.", "details": "Implement a user management system with the following features:\n\n1. User listing with advanced filtering and search\n2. Detailed user profiles with activity history\n3. Role and permission management\n4. Account status controls (activate, suspend, delete)\n5. Bulk user operations\n6. User impersonation for troubleshooting\n7. Communication tools for user outreach\n\nImplement server-side pagination and filtering for performance with large user bases. Use Supabase RLS policies to ensure secure access to user data.\n\nKey libraries:\n- @tanstack/react-table@8.10.7 (for user tables)\n- @supabase/auth-helpers-nextjs@0.8.1 (for user management)\n- @tanstack/react-query@5.8.4 (for data fetching)\n- csv-stringify@6.4.4 (for user exports)\n\nImplement as client components with server actions for user management operations.", "testStrategy": "1. Unit tests for management components\n2. Integration tests for user operations\n3. Test role and permission changes\n4. Verify account status controls\n5. Test bulk operations\n6. Validate security measures\n7. End-to-end test of user management workflows", "priority": "medium", "dependencies": [13], "status": "pending", "subtasks": []}, {"id": 16, "title": "Implement Payout Management System", "description": "Create a system for processing and tracking instructor payouts, including payment history, revenue sharing, and financial reporting.", "details": "Develop a comprehensive payout management system:\n\n1. Payout dashboard with pending and processed payments\n2. Revenue calculation based on course sales\n3. Payment processing workflow\n4. Transaction history and reconciliation\n5. Tax document management\n6. Payment method management for instructors\n7. Financial reporting and exports\n\nImplement secure payment processing with proper audit trails. Use a reliable payment processing service for actual transfers. Create a revenue sharing model based on platform policies.\n\nKey libraries and technologies:\n- @tanstack/react-table@8.10.7 (for payment tables)\n- date-fns@2.30.0 (for date handling)\n- recharts@2.9.3 (for financial visualization)\n- Stripe Connect or PayPal API (for payment processing)\n- xlsx@0.18.5 (for financial exports)\n\nImplement as client components with server actions for payment processing.", "testStrategy": "1. Unit tests for payout components\n2. Integration tests for payment calculations\n3. Test payment processing workflow\n4. Verify financial reporting accuracy\n5. Test export functionality\n6. Validate security measures\n7. End-to-end test of payout management\n8. Security testing for payment operations", "priority": "medium", "dependencies": [7, 13], "status": "pending", "subtasks": []}]}