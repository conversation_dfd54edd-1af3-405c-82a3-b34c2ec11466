# Task ID: 14
# Title: Implement Instructor Application Review System
# Status: pending
# Dependencies: 5, 13
# Priority: medium
# Description: Create a system for administrators to review, approve, or reject instructor applications with feedback capabilities.
# Details:
Develop an instructor application review system:

1. Application queue with status filtering
2. Detailed application view with all submitted information
3. Document viewer for supporting materials
4. Approval/rejection workflow with reason selection
5. Feedback mechanism for rejected applications
6. Automatic email notifications for status changes
7. Application statistics and processing metrics

Implement a workflow system for application states (Pending, Under Review, Approved, Rejected). Use Supabase real-time features for live updates to the application queue.

Key libraries and technologies:
- @tanstack/react-table@8.10.7 (for application queue)
- react-pdf@7.5.1 (for document viewing)
- @supabase/realtime-js (for real-time updates)
- Resend.com or SendGrid (for email notifications)

Implement as client components with server actions for application processing.

# Test Strategy:
1. Unit tests for review components
2. Integration tests for application processing
3. Test email notification system
4. Verify real-time updates
5. Test document viewing functionality
6. Validate workflow state transitions
7. End-to-end test of application review process
