{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/coursat/src/app/api/context7/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// Mock data for Context7 information\nconst context7Info = {\n  name: \"Context7\",\n  version: \"1.0.6\",\n  description: \"Context7 MCP Server -- Up-to-date documentation for LLMs and AI code editors\",\n  repository: \"https://github.com/upstash/context7\",\n  features: [\n    \"Real-time documentation access\",\n    \"MCP (Model Context Protocol) integration\",\n    \"Support for multiple AI clients\",\n    \"Up-to-date library information\",\n    \"Enhanced AI development workflows\"\n  ],\n  supportedClients: [\n    { name: \"Claude Desktop\", supported: true },\n    { name: \"<PERSON>urs<PERSON>\", supported: true },\n    { name: \"<PERSON>surf\", supported: true },\n    { name: \"<PERSON><PERSON><PERSON><PERSON>\", supported: true },\n    { name: \"<PERSON><PERSON>\", supported: true },\n    { name: \"VS Code\", supported: true }\n  ],\n  installation: {\n    quick: \"npx -y @smithery/cli install @upstash/context7-mcp --client claude\",\n    manual: {\n      command: \"npx\",\n      args: [\"-y\", \"@upstash/context7-mcp@latest\"]\n    }\n  },\n  stats: {\n    totalLibraries: 50000,\n    codeSnippets: 100000,\n    lastUpdated: new Date().toISOString()\n  }\n};\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const info = searchParams.get('info');\n\n    // Return specific information if requested\n    if (info) {\n      switch (info) {\n        case 'features':\n          return NextResponse.json({ features: context7Info.features });\n        case 'clients':\n          return NextResponse.json({ supportedClients: context7Info.supportedClients });\n        case 'installation':\n          return NextResponse.json({ installation: context7Info.installation });\n        case 'stats':\n          return NextResponse.json({ stats: context7Info.stats });\n        default:\n          return NextResponse.json({ error: 'Invalid info parameter' }, { status: 400 });\n      }\n    }\n\n    // Return all information\n    return NextResponse.json(context7Info);\n  } catch (error) {\n    console.error('Error in Context7 API:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, data } = body;\n\n    switch (action) {\n      case 'validate-installation':\n        // Mock validation of Context7 installation\n        const isValid = data?.command === 'npx' && \n                       data?.args?.includes('@upstash/context7-mcp@latest');\n        \n        return NextResponse.json({\n          valid: isValid,\n          message: isValid \n            ? 'Context7 installation configuration is valid' \n            : 'Invalid Context7 installation configuration'\n        });\n\n      case 'check-compatibility':\n        // Mock compatibility check\n        const client = data?.client?.toLowerCase();\n        const supportedClient = context7Info.supportedClients.find(\n          c => c.name.toLowerCase().includes(client)\n        );\n\n        return NextResponse.json({\n          compatible: !!supportedClient,\n          client: supportedClient || null,\n          message: supportedClient \n            ? `${supportedClient.name} is supported by Context7`\n            : 'Client not found or not supported'\n        });\n\n      default:\n        return NextResponse.json(\n          { error: 'Invalid action' },\n          { status: 400 }\n        );\n    }\n  } catch (error) {\n    console.error('Error in Context7 POST API:', error);\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,qCAAqC;AACrC,MAAM,eAAe;IACnB,MAAM;IACN,SAAS;IACT,aAAa;IACb,YAAY;IACZ,UAAU;QACR;QACA;QACA;QACA;QACA;KACD;IACD,kBAAkB;QAChB;YAAE,MAAM;YAAkB,WAAW;QAAK;QAC1C;YAAE,MAAM;YAAU,WAAW;QAAK;QAClC;YAAE,MAAM;YAAY,WAAW;QAAK;QACpC;YAAE,MAAM;YAAU,WAAW;QAAK;QAClC;YAAE,MAAM;YAAO,WAAW;QAAK;QAC/B;YAAE,MAAM;YAAW,WAAW;QAAK;KACpC;IACD,cAAc;QACZ,OAAO;QACP,QAAQ;YACN,SAAS;YACT,MAAM;gBAAC;gBAAM;aAA+B;QAC9C;IACF;IACA,OAAO;QACL,gBAAgB;QAChB,cAAc;QACd,aAAa,IAAI,OAAO,WAAW;IACrC;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,2CAA2C;QAC3C,IAAI,MAAM;YACR,OAAQ;gBACN,KAAK;oBACH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,UAAU,aAAa,QAAQ;oBAAC;gBAC7D,KAAK;oBACH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,kBAAkB,aAAa,gBAAgB;oBAAC;gBAC7E,KAAK;oBACH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,cAAc,aAAa,YAAY;oBAAC;gBACrE,KAAK;oBACH,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,OAAO,aAAa,KAAK;oBAAC;gBACvD;oBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBAAE,OAAO;oBAAyB,GAAG;wBAAE,QAAQ;oBAAI;YAChF;QACF;QAEA,yBAAyB;QACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QAEzB,OAAQ;YACN,KAAK;gBACH,2CAA2C;gBAC3C,MAAM,UAAU,MAAM,YAAY,SACnB,MAAM,MAAM,SAAS;gBAEpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,OAAO;oBACP,SAAS,UACL,iDACA;gBACN;YAEF,KAAK;gBACH,2BAA2B;gBAC3B,MAAM,SAAS,MAAM,QAAQ;gBAC7B,MAAM,kBAAkB,aAAa,gBAAgB,CAAC,IAAI,CACxD,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAGrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,YAAY,CAAC,CAAC;oBACd,QAAQ,mBAAmB;oBAC3B,SAAS,kBACL,GAAG,gBAAgB,IAAI,CAAC,yBAAyB,CAAC,GAClD;gBACN;YAEF;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAiB,GAC1B;oBAAE,QAAQ;gBAAI;QAEpB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}