# Task ID: 5
# Title: Create Instructor Application Wizard
# Status: pending
# Dependencies: 4
# Priority: medium
# Description: Develop a 3-step instructor application wizard that collects personal information, teaching experience, and motivation for becoming an instructor.
# Details:
Implement a multi-step application wizard for instructor verification:

1. Create a wizard component with step tracking and navigation
2. Step 1: Personal information form (bio, profile picture, credentials)
3. Step 2: Teaching experience form (subjects, years of experience, certifications)
4. Step 3: Motivation statement and agreement to terms
5. Implement form validation for each step using react-hook-form and zod
6. Create Applications table in Supabase with required fields
7. Add file upload for supporting documents using Supabase Storage
8. Implement application submission with status tracking

Use a state management approach like React Context or Zustand to maintain form state across steps. Implement auto-save functionality to prevent data loss.

Key libraries:
- react-hook-form@7.48.2
- zod@3.22.4
- zustand@4.4.6 (for state management)
- @supabase/storage-js (for file uploads)
- react-dropzone@14.2.3 (for file upload UI)

Implement as client components with server actions for form submission.

# Test Strategy:
1. Unit tests for form validation in each step
2. Integration tests for wizard navigation
3. Test file upload functionality
4. Verify data persistence between steps
5. Test form submission and database updates
6. Validate error handling and user feedback
7. End-to-end test of complete application process
