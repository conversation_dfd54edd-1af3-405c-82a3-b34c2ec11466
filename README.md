# Next.js + Context7 Project

A modern Next.js application enhanced with Context7 MCP (Model Context Protocol) for AI-powered development workflows. This project demonstrates the integration of Context7 with Next.js to create a powerful development environment that leverages AI assistance.

## 🚀 Features

- **Next.js 15** with App Router
- **TypeScript** for type safety
- **Tailwind CSS** for styling
- **Context7 MCP** integration for AI development
- **Responsive Design** with dark mode support
- **Modern Components** with reusable architecture
- **API Routes** for backend functionality
- **SEO Optimized** with proper metadata

## 🛠️ Tech Stack

- [Next.js 15](https://nextjs.org/) - React framework
- [TypeScript](https://www.typescriptlang.org/) - Type safety
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Context7](https://github.com/upstash/context7) - MCP for AI development
- [ESLint](https://eslint.org/) - Code linting

## 📋 Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun
- An AI development environment that supports MCP (<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, etc.)

## 🚀 Getting Started

### 1. <PERSON><PERSON> and Install

```bash
# Clone the repository
git clone <your-repo-url>
cd coursat

# Install dependencies
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

### 2. Set up Context7 MCP

#### Quick Installation (Recommended)
```bash
npx -y @smithery/cli install @upstash/context7-mcp --client claude
```

#### Manual Configuration
Add to your MCP configuration file:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

### 3. Start Development Server

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   │   └── context7/      # Context7 API endpoints
│   ├── context7/          # Context7 documentation page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── Header.tsx         # Navigation header
│   └── Footer.tsx         # Site footer
public/                    # Static assets
├── next.svg
├── vercel.svg
└── ...
```

## 🎯 Context7 Integration

This project showcases Context7 MCP integration in several ways:

### 1. **Documentation Page** (`/context7`)
- Comprehensive guide to Context7
- Installation instructions
- Supported AI clients
- Benefits and features

### 2. **API Integration** (`/api/context7`)
- RESTful API for Context7 information
- Installation validation
- Compatibility checking
- Real-time stats

### 3. **Component Architecture**
- Reusable components for Context7 features
- Responsive design with Tailwind CSS
- Dark mode support

## 🔧 Available Scripts

```bash
# Development
npm run dev          # Start development server

# Production
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
```

## 🌐 API Endpoints

### GET `/api/context7`
Get Context7 information

**Query Parameters:**
- `info` (optional): Specific information (`features`, `clients`, `installation`, `stats`)

**Example:**
```bash
curl http://localhost:3000/api/context7?info=features
```

### POST `/api/context7`
Validate Context7 configuration

**Body:**
```json
{
  "action": "validate-installation",
  "data": {
    "command": "npx",
    "args": ["-y", "@upstash/context7-mcp@latest"]
  }
}
```

## 🎨 Customization

### Styling
- Modify `src/app/globals.css` for global styles
- Update Tailwind configuration in `tailwind.config.ts`
- Customize components in `src/components/`

### Content
- Update home page content in `src/app/page.tsx`
- Modify Context7 documentation in `src/app/context7/page.tsx`
- Add new pages in the `src/app/` directory

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy automatically

### Other Platforms
- **Netlify**: Connect GitHub repository
- **Railway**: Deploy with `railway up`
- **Docker**: Use the included Dockerfile (if added)

## 📚 Learn More

### Next.js Resources
- [Next.js Documentation](https://nextjs.org/docs)
- [Learn Next.js](https://nextjs.org/learn)
- [Next.js GitHub](https://github.com/vercel/next.js)

### Context7 Resources
- [Context7 GitHub](https://github.com/upstash/context7)
- [MCP Documentation](https://modelcontextprotocol.io/)
- [Upstash](https://upstash.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js Team](https://nextjs.org/) for the amazing framework
- [Upstash](https://upstash.com/) for Context7 MCP
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first CSS framework
- [Vercel](https://vercel.com/) for hosting and deployment platform
