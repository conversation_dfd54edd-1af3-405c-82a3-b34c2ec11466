# Task ID: 11
# Title: Build Course Detail Pages
# Status: pending
# Dependencies: 8, 10
# Priority: medium
# Description: Create detailed course pages with curriculum preview, instructor information, student reviews, and enrollment functionality.
# Details:
Implement comprehensive course detail pages:

1. Course header with title, instructor, and key metrics
2. Course description and learning objectives
3. Curriculum preview with expandable sections
4. Instructor profile and credentials
5. Student reviews and ratings
6. Related and recommended courses
7. Enrollment and payment options
8. Preview video or sample lesson

Use Open Graph tags and structured data for improved social sharing and SEO. Implement video previews using Bunny.net's player. Create a responsive layout that works well on all devices.

Key libraries and technologies:
- bunny-stream-player@1.0.0 (for video preview)
- schema-dts@1.1.2 (for structured data)
- next/metadata (for SEO optimization)
- @vercel/og (for dynamic Open Graph images)

Implement as server components with client islands for interactive elements.

# Test Strategy:
1. Unit tests for page components
2. Integration tests for data fetching and display
3. Test video preview functionality
4. Verify responsive layout across device sizes
5. Test SEO elements and structured data
6. Validate enrollment flow
7. End-to-end test of course detail page experience
