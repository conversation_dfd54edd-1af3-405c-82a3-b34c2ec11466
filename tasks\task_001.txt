# Task ID: 1
# Title: Set up Next.js 15 project with TypeScript and App Router
# Status: done
# Dependencies: None
# Priority: high
# Description: Initialize the project with Next.js 15, TypeScript, and the new App Router for optimal performance and type safety.
# Details:
Use create-next-app to set up the project:
1. Run `npx create-next-app@latest context7-integration --typescript --eslint --tailwind --app`
2. Choose 'Yes' for all default options
3. Update next.config.js to enable static exports:
```javascript
module.exports = {
  output: 'export',
  // other config options...
}
```
4. Set up a basic folder structure in the 'app' directory

# Test Strategy:
Verify project structure, run `npm run dev` to ensure the development server starts without errors, and check that TypeScript compilation succeeds.
