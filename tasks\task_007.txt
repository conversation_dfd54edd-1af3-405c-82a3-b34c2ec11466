# Task ID: 7
# Title: Build Instructor Dashboard
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Create a comprehensive instructor dashboard with course management, student analytics, and revenue tracking.
# Details:
Implement an instructor dashboard with the following features:

1. Dashboard overview with key metrics (students, courses, revenue)
2. Course management section (list, create, edit, delete)
3. Student management with enrollment data
4. Analytics section with charts for engagement and revenue
5. Payout history and upcoming payments
6. School settings management
7. Real-time notifications using Supabase Realtime

Use a dashboard layout with responsive sidebar navigation. Implement data visualization with Recharts or Chart.js. Use Supabase real-time subscriptions for live updates.

Key libraries:
- recharts@2.9.3 (for data visualization)
- @tanstack/react-table@8.10.7 (for data tables)
- @supabase/realtime-js (for real-time updates)
- date-fns@2.30.0 (for date formatting)

Implement as a mix of client and server components, with server components for data fetching and client components for interactive elements.

# Test Strategy:
1. Unit tests for dashboard components
2. Integration tests for data fetching and display
3. Test real-time update functionality
4. Verify responsive layout across device sizes
5. Test data visualization components
6. Validate navigation and routing
7. End-to-end test of instructor workflows
