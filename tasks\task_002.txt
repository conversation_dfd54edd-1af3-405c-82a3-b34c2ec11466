# Task ID: 2
# Title: Implement responsive component library with Tailwind CSS
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create a set of reusable UI components styled with Tailwind CSS 4, including dark mode support.
# Details:
1. Set up Tailwind CSS 4 (already included in create-next-app)
2. Create components in `app/components` folder:
   - Button.tsx
   - Card.tsx
   - Navbar.tsx
   - Footer.tsx
   - Layout.tsx
3. Implement dark mode toggle using Next.js 13's new server components:
```typescript
// app/components/ThemeToggle.tsx
'use client'
import { useTheme } from 'next-themes'

export const ThemeToggle = () => {
  const { theme, setTheme } = useTheme()
  return (
    <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
      Toggle Theme
    </button>
  )
}
```
4. Use @apply directives in Tailwind for common styles

# Test Strategy:
Create storybook stories for each component, test responsiveness across devices, and verify dark mode functionality.
