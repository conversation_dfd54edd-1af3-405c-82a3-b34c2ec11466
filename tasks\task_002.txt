# Task ID: 2
# Title: Implement User Registration System
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Create a comprehensive user registration system with role selection (Student, Instructor, Admin) and email verification functionality.
# Details:
Implement user registration using Supabase Auth and Database:

1. Create registration form with fields: name, email, password, role selection
2. Set up form validation using react-hook-form and zod
3. Implement Supabase Auth signUp method with email confirmation
4. Create users table in Supabase with required fields (id, email, name, role, status, created_at)
5. Add role-based redirection after registration
6. Implement error handling and user feedback

Create custom registration hooks and context providers for auth state management. Use Supabase Row Level Security (RLS) policies to secure user data.

Key libraries and versions:
- @supabase/auth-helpers-nextjs@0.8.1
- @supabase/supabase-js@2.38.4
- react-hook-form@7.48.2
- zod@3.22.4

Implement as client components with server actions for form submission.

# Test Strategy:
1. Unit tests for form validation logic
2. Integration tests for registration flow
3. Test error handling for duplicate emails
4. Verify role selection functionality
5. Test form validation for all required fields
6. End-to-end test of complete registration process
7. Security testing for proper data handling
