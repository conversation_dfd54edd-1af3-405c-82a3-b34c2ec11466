# Context7 Integration Guide

This document provides a comprehensive guide for integrating Context7 MCP (Model Context Protocol) with your development environment to enhance AI-powered development workflows.

## What is Context7?

Context7 is a Model Context Protocol (MCP) server that provides up-to-date documentation for LLMs and AI code editors. It enables AI assistants to access real-time, accurate documentation for thousands of libraries and frameworks, resulting in better code suggestions and more helpful development assistance.

## Benefits

- **Real-time Documentation**: Access the latest documentation for libraries and frameworks
- **Enhanced AI Responses**: AI assistants provide more accurate and contextual suggestions
- **Faster Development**: Reduce time spent searching for documentation
- **Better Code Quality**: Follow current best practices with up-to-date examples
- **Seamless Integration**: Works with popular AI development environments

## Supported AI Clients

Context7 supports the following AI development environments:

### Primary Clients
- **<PERSON> Desktop** - An<PERSON><PERSON>'s desktop application
- **Cursor** - AI-powered code editor
- **Windsurf** - AI development environment
- **BoltAI** - AI assistant for developers

### Additional Clients
- **Zed** - High-performance code editor
- **VS Code** - With MCP extensions

## Installation Methods

### Method 1: Quick Install with <PERSON><PERSON> (Recommended)

The fastest way to install Context7 is using the Smithery CLI:

```bash
npx -y @smithery/cli install @upstash/context7-mcp --client claude
```

This command automatically:
- Installs the Context7 MCP server
- Configures it for your specified client (Claude in this example)
- Sets up the necessary configuration files

### Method 2: Manual Configuration

For manual setup or custom configurations, add Context7 to your MCP configuration file:

#### For Claude Desktop
Add to `~/.config/claude/claude_desktop_config.json` (macOS/Linux) or `%APPDATA%\Claude\claude_desktop_config.json` (Windows):

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

#### For Cursor
Add to your Cursor MCP configuration:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

#### For Windsurf
Add to your Windsurf MCP configuration:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

### Method 3: Alternative Package Managers

#### Using Bun
```json
{
  "mcpServers": {
    "context7": {
      "command": "bunx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

#### Using Deno
```json
{
  "mcpServers": {
    "context7": {
      "command": "deno",
      "args": ["run", "--allow-env", "--allow-net", "npm:@upstash/context7-mcp"]
    }
  }
}
```

## Configuration Options

### Environment Variables

You can customize Context7 behavior using environment variables:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {
        "DEFAULT_MINIMUM_TOKENS": "10000"
      }
    }
  }
}
```

### Windows Configuration

For Windows users, use the `cmd` command wrapper:

```json
{
  "mcpServers": {
    "context7": {
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"],
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

## Verification

### Testing the Installation

You can test your Context7 installation using the MCP Inspector:

```bash
npx -y @modelcontextprotocol/inspector npx @upstash/context7-mcp@latest
```

This will open a web interface where you can test the MCP server functionality.

### Checking Client Integration

1. Restart your AI client after configuration
2. Look for Context7 in the available MCP servers list
3. Test by asking your AI assistant about a specific library or framework
4. The responses should include more detailed and up-to-date information

## Troubleshooting

### Common Issues

#### ERR_MODULE_NOT_FOUND
If you encounter module resolution errors, try using `bunx` instead of `npx`:

```json
{
  "mcpServers": {
    "context7": {
      "command": "bunx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}
```

#### ESM Resolution Issues
For ESM-related errors, add the experimental VM modules flag:

```json
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": [
        "-y",
        "--node-options=--experimental-vm-modules",
        "@upstash/context7-mcp@1.0.6"
      ]
    }
  }
}
```

### Docker Installation

For containerized environments, you can use Docker:

```dockerfile
FROM node:18-alpine
WORKDIR /app
RUN npm install -g @upstash/context7-mcp@latest
CMD ["context7-mcp"]
```

Build and run:
```bash
docker build -t context7-mcp .
docker run -i --rm context7-mcp
```

## Development Workflow

### Using Context7 in Your Projects

1. **Project Setup**: Install Context7 in your development environment
2. **AI Assistance**: Use your AI client to get help with:
   - Library documentation
   - Code examples
   - Best practices
   - API references
3. **Enhanced Productivity**: Leverage real-time documentation access for faster development

### Example Prompts

With Context7 installed, you can ask your AI assistant:

- "Show me the latest Next.js App Router documentation"
- "How do I use the newest features in React 19?"
- "What's the current best practice for TypeScript configuration?"
- "Generate a PostgreSQL query using the latest syntax"

## Best Practices

1. **Keep Updated**: Regularly update Context7 to access the latest documentation
2. **Specific Queries**: Be specific in your questions to get the most relevant information
3. **Verify Information**: While Context7 provides up-to-date docs, always verify critical information
4. **Combine Sources**: Use Context7 alongside official documentation for comprehensive understanding

## Support and Resources

- **GitHub Repository**: [https://github.com/upstash/context7](https://github.com/upstash/context7)
- **Issues and Bug Reports**: Use the GitHub issues page
- **Documentation**: Check the repository README for the latest updates
- **Community**: Join discussions in the repository's discussion section

## Contributing

Context7 is an open-source project. You can contribute by:

1. Reporting bugs and issues
2. Suggesting new features
3. Contributing code improvements
4. Improving documentation
5. Sharing your integration experiences

---

*This guide is part of the Next.js + Context7 project. For project-specific information, see the main README.md file.*
