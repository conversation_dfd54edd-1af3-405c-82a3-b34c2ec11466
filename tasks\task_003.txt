# Task ID: 3
# Title: Build Login System with Role-Based Redirects
# Status: pending
# Dependencies: 2
# Priority: high
# Description: Develop a secure login system with role-based redirects, session management, and error handling.
# Details:
Implement login functionality using Supabase Auth:

1. Create login form with email/password fields
2. Implement form validation with react-hook-form and zod
3. Use Supabase signInWithPassword method for authentication
4. Set up middleware for role-based redirects:
   - Students → Student Dashboard
   - Instructors → Instructor Dashboard
   - Admins → Admin Dashboard
5. Implement session persistence with Supabase Auth Helpers
6. Add "Remember me" functionality
7. Create password reset flow

Use Next.js middleware to protect routes based on authentication status and user role. Implement CSRF protection and rate limiting for security.

Key libraries:
- @supabase/auth-helpers-nextjs@0.8.1
- @supabase/supabase-js@2.38.4
- react-hook-form@7.48.2
- zod@3.22.4
- next-auth@4.24.5 (optional for additional auth features)

Implement as client components with server actions for form submission.

# Test Strategy:
1. Unit tests for login form validation
2. Integration tests for authentication flow
3. Test role-based redirects for each user type
4. Verify error handling for invalid credentials
5. Test session persistence across page refreshes
6. Security testing for brute force protection
7. End-to-end test of complete login process
