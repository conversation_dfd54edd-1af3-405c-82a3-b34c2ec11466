# Task ID: 15
# Title: Build User Management System
# Status: pending
# Dependencies: 13
# Priority: medium
# Description: Create a comprehensive user management system for administrators to view, edit, and manage all platform users.
# Details:
Implement a user management system with the following features:

1. User listing with advanced filtering and search
2. Detailed user profiles with activity history
3. Role and permission management
4. Account status controls (activate, suspend, delete)
5. Bulk user operations
6. User impersonation for troubleshooting
7. Communication tools for user outreach

Implement server-side pagination and filtering for performance with large user bases. Use Supabase RLS policies to ensure secure access to user data.

Key libraries:
- @tanstack/react-table@8.10.7 (for user tables)
- @supabase/auth-helpers-nextjs@0.8.1 (for user management)
- @tanstack/react-query@5.8.4 (for data fetching)
- csv-stringify@6.4.4 (for user exports)

Implement as client components with server actions for user management operations.

# Test Strategy:
1. Unit tests for management components
2. Integration tests for user operations
3. Test role and permission changes
4. Verify account status controls
5. Test bulk operations
6. Validate security measures
7. End-to-end test of user management workflows
