import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Next.js + Context7 | AI-Powered Development",
  description: "A modern Next.js application enhanced with Context7 MCP for AI-powered development workflows. Built with TypeScript, Tailwind CSS, and the latest web technologies.",
  keywords: ["Next.js", "Context7", "MCP", "AI", "Development", "TypeScript", "Tailwind CSS"],
  authors: [{ name: "Next.js + Context7 Team" }],
  openGraph: {
    title: "Next.js + Context7 | AI-Powered Development",
    description: "A modern Next.js application enhanced with Context7 MCP for AI-powered development workflows.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
