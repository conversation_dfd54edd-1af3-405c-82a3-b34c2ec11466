import Link from "next/link";

export default function Context7Page() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-block bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg font-bold text-2xl mb-4">
            Context7
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
            Model Context Protocol for AI Development
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Context7 is an MCP (Model Context Protocol) server that provides up-to-date documentation 
            for LLMs and AI code editors, enhancing your development workflow with intelligent assistance.
          </p>
        </div>

        {/* What is Context7 */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">What is Context7?</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                🤖 AI-Powered Documentation
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Context7 provides real-time, up-to-date documentation access for AI assistants, 
                enabling them to give more accurate and current advice about libraries, frameworks, and tools.
              </p>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                🔌 MCP Integration
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                Built on the Model Context Protocol standard, Context7 seamlessly integrates with 
                popular AI development environments like Claude, Cursor, Windsurf, and more.
              </p>
            </div>
          </div>
        </div>

        {/* Installation Guide */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Installation & Setup</h2>
          
          <div className="space-y-8">
            {/* Quick Install */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                🚀 Quick Install (Recommended)
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                Use Smithery CLI for automatic installation:
              </p>
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <code className="text-sm text-gray-800 dark:text-gray-200">
                  npx -y @smithery/cli install @upstash/context7-mcp --client claude
                </code>
              </div>
            </div>

            {/* Manual Configuration */}
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                ⚙️ Manual Configuration
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-3">
                For manual setup, add to your MCP configuration:
              </p>
              <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
                <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
{`{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"]
    }
  }
}`}
                </pre>
              </div>
            </div>
          </div>
        </div>

        {/* Supported Clients */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Supported AI Clients</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { name: "Claude Desktop", icon: "🤖" },
              { name: "Cursor", icon: "🖱️" },
              { name: "Windsurf", icon: "🏄" },
              { name: "BoltAI", icon: "⚡" },
              { name: "Zed", icon: "⚡" },
              { name: "VS Code", icon: "💻" }
            ].map((client) => (
              <div key={client.name} className="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <span className="text-2xl">{client.icon}</span>
                <span className="font-medium text-gray-900 dark:text-white">{client.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Benefits for Development</h2>
          <div className="grid md:grid-cols-2 gap-8">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 dark:text-green-400">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Up-to-date Documentation</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Access the latest documentation for thousands of libraries and frameworks
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 dark:text-green-400">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Enhanced AI Responses</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    AI assistants provide more accurate and contextual code suggestions
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 dark:text-green-400">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Faster Development</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Reduce time spent searching for documentation and examples
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-green-600 dark:text-green-400">✓</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">Better Code Quality</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Follow best practices with current documentation and examples
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <a
              href="https://github.com/upstash/context7"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-blue-700 transition-all duration-200 shadow-lg"
            >
              View on GitHub
            </a>
            <Link
              href="/"
              className="border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
