<context>
# Overview
Coursat is a comprehensive online learning platform that enables instructors to create their own branded schools and sell courses while providing students with an engaging learning experience. The platform serves three main user types: Students who want to learn from experts, Instructors who want to create and sell courses, and Administrators who manage the platform.

The application solves the problem of fragmented online education by providing a unified platform where instructors can build their own branded schools, create rich multimedia courses, and manage their students, while students get access to high-quality courses from verified instructors in a seamless learning environment.

# Core Features
1. **Authentication System** - Complete user registration, login, email verification with role-based access
2. **Instructor Application Process** - 3-step wizard for instructor verification and onboarding
3. **School Creation & Management** - Branded school setup with custom domains and branding
4. **Course Creation Tools** - Rich course builder with video, text, and PDF content support
5. **Student Learning Experience** - Course browsing, enrollment, and interactive learning interface
6. **Admin Management Panel** - User management, application review, and payout processing
7. **Payment & Revenue System** - Course sales, instructor payouts, and financial tracking

# User Experience
**Primary Users:**
- Students: Individuals seeking to learn new skills from expert instructors
- Instructors: Subject matter experts who want to monetize their knowledge by creating courses
- Administrators: Platform managers who oversee operations, user management, and payouts

**Key User Flows:**
1. Student: Register → Browse Schools/Courses → Enroll → Learn → Track Progress
2. Instructor: Apply → Get Approved → Create School → Build Courses → Manage Students → Receive Payouts
3. Admin: Review Applications → Manage Users → Process Payouts → Monitor Platform Health

**UI/UX Considerations:**
- Mobile-first responsive design with RTL support for Arabic content
- Branded school experiences with custom colors and logos
- Intuitive course creation tools with drag-and-drop functionality
- Seamless video streaming and content delivery
- Real-time progress tracking and engagement features
</context>

<PRD>
# Technical Architecture
**System Components:**
- Next.js 14 frontend with App Router and TypeScript
- Tailwind CSS with Shadcn UI components for consistent design
- Supabase for authentication, database, and real-time features
- Bunny.net CDN for video streaming and file storage
- Role-based access control (Student, Instructor, Admin)
- Rich text editor for course content creation

**Data Models:**
- Users (id, email, name, role, status, created_at)
- Schools (id, instructor_id, name, subdomain, branding, settings)
- Courses (id, school_id, title, description, price, status, content)
- Enrollments (id, student_id, course_id, progress, completed_at)
- Applications (id, user_id, status, personal_info, experience, motivation)
- Payouts (id, instructor_id, amount, status, processed_at)

**APIs and Integrations:**
- Supabase Auth for user management and authentication
- Supabase Database for data persistence and real-time updates
- Bunny.net API for video upload, processing, and streaming
- Email service for verification and notifications
- Payment processing for course purchases and instructor payouts
- Subdomain routing for branded school experiences

**Infrastructure Requirements:**
- Next.js 14 with TypeScript and App Router
- Supabase backend (auth, database, storage, real-time)
- Bunny.net for video CDN and streaming
- Vercel for hosting and deployment
- Custom domain management for school subdomains

# Development Roadmap
**Phase 1: Authentication & Core Infrastructure (Tasks 1-4)**
- Landing page with modern design and clear value proposition
- User registration with role selection and email verification
- Login system with role-based redirects and error handling
- Email verification flow with resend functionality

**Phase 2: Instructor Onboarding (Tasks 5-7)**
- 3-step instructor application wizard with validation
- School setup wizard with branding and subdomain selection
- Instructor dashboard with course management and analytics

**Phase 3: Course Creation & Management (Task 8)**
- Rich course builder with video, text, and PDF support
- Drag-and-drop lesson organization
- Video upload integration with Bunny.net
- Auto-save and validation features

**Phase 4: Student Experience (Tasks 9-12)**
- Student dashboard with course progress tracking
- School catalog with search and filtering
- Detailed course pages with preview functionality
- Interactive course player with progress tracking

**Phase 5: Admin Management (Tasks 13-16)**
- Admin dashboard with platform metrics and analytics
- Instructor application review system
- User management with role and status controls
- Payout management system for instructor payments

# Logical Dependency Chain
**Foundation (Priority 1):**
1. Landing Page - Entry point and marketing
2. Registration System - User onboarding
3. Login System - Authentication flow
4. Email Verification - Account security

**Instructor Flow (Priority 2):**
5. Instructor Application - Verification process
6. School Setup - Branded experience creation
7. Instructor Dashboard - Management interface
8. Course Creation - Content development tools

**Student Flow (Priority 3):**
9. Student Dashboard - Learning hub
10. Course Browse - Discovery interface
11. Course Detail - Information and enrollment
12. Course Player - Learning experience

**Admin Flow (Priority 4):**
13. Admin Dashboard - Platform oversight
14. Application Review - Instructor approval
15. User Management - Platform administration
16. Payout Management - Financial operations

# Risks and Mitigations
**Technical Challenges:**
- Video streaming performance → Use Bunny.net CDN with adaptive bitrate
- Real-time features complexity → Leverage Supabase real-time subscriptions
- Subdomain routing → Implement Next.js middleware for custom domains
- File upload security → Validate file types and implement virus scanning

**MVP Scope:**
- Focus on core learning platform functionality
- Prioritize instructor and student essential features
- Build scalable architecture for future enhancements
- Implement basic payment processing before advanced features

**Resource Constraints:**
- Use Supabase for rapid backend development
- Leverage Shadcn UI for consistent component library
- Implement progressive enhancement for advanced features
- Focus on mobile-first responsive design

# Appendix
**Research Findings:**
- Online learning platforms need seamless video streaming
- Instructor onboarding is critical for platform success
- Students expect mobile-responsive learning experiences
- RTL support is essential for Arabic-speaking markets

**Technical Specifications:**
- Next.js 14 with App Router and TypeScript
- Supabase for backend services (auth, database, storage)
- Tailwind CSS with Shadcn UI components
- Bunny.net for video CDN and streaming
- Role-based access control with three user types
- Mobile-first responsive design with RTL support
- SEO optimization for course discovery
</PRD>
