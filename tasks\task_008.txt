# Task ID: 8
# Title: Develop Course Creation Tools
# Status: pending
# Dependencies: 7
# Priority: high
# Description: Build a rich course builder with support for video, text, and PDF content, including drag-and-drop lesson organization and Bunny.net integration.
# Details:
Implement comprehensive course creation tools:

1. Course structure builder with sections and lessons
2. Rich text editor for lesson content using TipTap or Lexical
3. Video upload integration with Bunny.net Stream
4. PDF and document upload functionality
5. Drag-and-drop lesson reordering using dnd-kit
6. Auto-save functionality with optimistic UI updates
7. Course settings (pricing, visibility, requirements)
8. Course preview functionality

Integrate with Bunny.net for video processing and streaming. Use their adaptive bitrate streaming for optimal playback across devices. Implement client-side and server-side validation for course structure.

Key libraries and services:
- @tiptap/react@2.1.12 (for rich text editing)
- @dnd-kit/core@6.0.8 and @dnd-kit/sortable@7.0.2 (for drag-and-drop)
- bunny-stream-js@1.0.0 (for Bunny.net integration)
- react-dropzone@14.2.3 (for file uploads)
- @tanstack/react-query@5.8.4 (for data fetching and caching)

Implement as client components with server actions for data persistence.

# Test Strategy:
1. Unit tests for editor components
2. Integration tests for content saving and retrieval
3. Test drag-and-drop functionality
4. Verify video upload and processing
5. Test auto-save functionality
6. Validate course structure validation
7. End-to-end test of course creation workflow
8. Performance testing for large courses
