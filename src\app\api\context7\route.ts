import { NextRequest, NextResponse } from 'next/server';

// Mock data for Context7 information
const context7Info = {
  name: "Context7",
  version: "1.0.6",
  description: "Context7 MCP Server -- Up-to-date documentation for LLMs and AI code editors",
  repository: "https://github.com/upstash/context7",
  features: [
    "Real-time documentation access",
    "MCP (Model Context Protocol) integration",
    "Support for multiple AI clients",
    "Up-to-date library information",
    "Enhanced AI development workflows"
  ],
  supportedClients: [
    { name: "Claude Desktop", supported: true },
    { name: "<PERSON>urs<PERSON>", supported: true },
    { name: "<PERSON>surf", supported: true },
    { name: "<PERSON><PERSON><PERSON><PERSON>", supported: true },
    { name: "<PERSON><PERSON>", supported: true },
    { name: "VS Code", supported: true }
  ],
  installation: {
    quick: "npx -y @smithery/cli install @upstash/context7-mcp --client claude",
    manual: {
      command: "npx",
      args: ["-y", "@upstash/context7-mcp@latest"]
    }
  },
  stats: {
    totalLibraries: 50000,
    codeSnippets: 100000,
    lastUpdated: new Date().toISOString()
  }
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const info = searchParams.get('info');

    // Return specific information if requested
    if (info) {
      switch (info) {
        case 'features':
          return NextResponse.json({ features: context7Info.features });
        case 'clients':
          return NextResponse.json({ supportedClients: context7Info.supportedClients });
        case 'installation':
          return NextResponse.json({ installation: context7Info.installation });
        case 'stats':
          return NextResponse.json({ stats: context7Info.stats });
        default:
          return NextResponse.json({ error: 'Invalid info parameter' }, { status: 400 });
      }
    }

    // Return all information
    return NextResponse.json(context7Info);
  } catch (error) {
    console.error('Error in Context7 API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    switch (action) {
      case 'validate-installation':
        // Mock validation of Context7 installation
        const isValid = data?.command === 'npx' && 
                       data?.args?.includes('@upstash/context7-mcp@latest');
        
        return NextResponse.json({
          valid: isValid,
          message: isValid 
            ? 'Context7 installation configuration is valid' 
            : 'Invalid Context7 installation configuration'
        });

      case 'check-compatibility':
        // Mock compatibility check
        const client = data?.client?.toLowerCase();
        const supportedClient = context7Info.supportedClients.find(
          c => c.name.toLowerCase().includes(client)
        );

        return NextResponse.json({
          compatible: !!supportedClient,
          client: supportedClient || null,
          message: supportedClient 
            ? `${supportedClient.name} is supported by Context7`
            : 'Client not found or not supported'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in Context7 POST API:', error);
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    );
  }
}
